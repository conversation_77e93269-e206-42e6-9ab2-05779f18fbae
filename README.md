# 企业微信回调API服务

基于Flask框架实现的企业微信消息接收和处理服务，支持消息加解密、URL验证等功能。

## 功能特性

- ✅ 企业微信回调URL验证
- ✅ 消息加解密处理
- ✅ 文本消息接收和回复
- ✅ 事件消息处理
- ✅ 健康检查接口
- ✅ 配置管理
- ✅ 日志记录

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置企业微信参数

复制配置文件模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，填写企业微信配置：
```bash
# 企业微信配置
WECOM_TOKEN=your_token_here
WECOM_ENCODING_AES_KEY=your_encoding_aes_key_here
WECOM_CORP_ID=your_corp_id_here
```

### 3. 启动服务

```bash
python start_server.py
```

或者直接运行：
```bash
python wecom_callback_server.py
```

## 配置说明

### 企业微信配置

在企业微信管理后台的应用设置中，需要配置以下参数：

1. **Token**: 用于验证消息来源的令牌
2. **EncodingAESKey**: 消息加解密密钥
3. **CorpID**: 企业ID

### 回调URL设置

在企业微信后台设置回调URL为：
```
http://your-domain.com:5000/wecom/callback
```

## API接口

### 回调接口
- **URL**: `/wecom/callback`
- **方法**: GET (URL验证) / POST (消息处理)
- **说明**: 企业微信回调接口

### 健康检查
- **URL**: `/health`
- **方法**: GET
- **返回**: 服务状态信息

### 配置查看
- **URL**: `/config`
- **方法**: GET
- **返回**: 当前配置状态（不包含敏感信息）

## 消息处理

### 支持的消息类型

1. **文本消息**: 自动回复收到的文本内容
2. **事件消息**: 处理关注/取消关注等事件

### 自定义消息处理

在 `WeComMessageHandler` 类中可以自定义消息处理逻辑：

```python
def handle_text_message(self, msg_data):
    # 解析消息
    xml_tree = ET.fromstring(msg_data)
    content = xml_tree.find("Content").text
    
    # 自定义处理逻辑
    reply_content = your_custom_logic(content)
    
    # 返回回复消息
    return self.create_text_reply(from_user, reply_content)
```

## 部署建议

### 开发环境
```bash
export FLASK_ENV=development
python start_server.py
```

### 生产环境
```bash
export FLASK_ENV=production
python start_server.py
```

建议使用 Gunicorn 或 uWSGI 等WSGI服务器部署：
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 wecom_callback_server:app
```

## 日志

服务会记录详细的日志信息，包括：
- URL验证过程
- 消息接收和处理
- 错误信息

日志文件位置：`wecom_callback.log`

## 故障排除

### 常见问题

1. **URL验证失败**
   - 检查Token、EncodingAESKey、CorpID是否正确
   - 确认服务器可以被企业微信访问

2. **消息解密失败**
   - 检查EncodingAESKey是否正确
   - 确认CorpID匹配

3. **依赖安装失败**
   - 确保Python版本兼容
   - 使用 `pip install pycryptodome` 替代 `pycrypto`

### 调试模式

启用调试模式查看详细信息：
```bash
export DEBUG=True
python start_server.py
```

## 许可证

MIT License
