#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
企业微信回调API服务
基于Flask框架实现企业微信消息接收和处理
"""

import os
import sys
import json
import time
import logging
from flask import Flask, request, jsonify, make_response
import xml.etree.ElementTree as ET

# 添加加解密库路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'weworkapi_python', 'callback'))

try:
    from WXBizMsgCrypt3 import WXBizMsgCrypt
except ImportError:
    from WXBizMsgCrypt import WXBizMsgCrypt

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

class WeComCallbackConfig:
    """企业微信回调配置"""
    def __init__(self):
        # 从环境变量或配置文件读取
        self.TOKEN = os.getenv('WECOM_TOKEN', 'your_token_here')
        self.ENCODING_AES_KEY = os.getenv('WECOM_ENCODING_AES_KEY', 'your_encoding_aes_key_here')
        self.CORP_ID = os.getenv('WECOM_CORP_ID', 'your_corp_id_here')
        
        # 验证配置
        if any(v == f'your_{k.lower()}_here' for k, v in self.__dict__.items()):
            logger.warning("请设置正确的企业微信配置参数")

config = WeComCallbackConfig()

class WeComMessageHandler:
    """企业微信消息处理器"""

    def __init__(self):
        # 检查配置是否有效
        if self._is_config_valid():
            try:
                self.wxcpt = WXBizMsgCrypt(config.TOKEN, config.ENCODING_AES_KEY, config.CORP_ID)
                logger.info("企业微信加解密组件初始化成功")
            except Exception as e:
                logger.error(f"企业微信加解密组件初始化失败: {e}")
                self.wxcpt = None
        else:
            logger.warning("企业微信配置无效，请设置正确的配置参数")
            self.wxcpt = None

        # 尝试导入自定义消息处理器
        try:
            from message_handlers import custom_handler
            self.custom_handler = custom_handler
            logger.info("已加载自定义消息处理器")
        except ImportError:
            logger.info("未找到自定义消息处理器，使用默认处理器")
            self.custom_handler = None

    def _is_config_valid(self):
        """检查配置是否有效"""
        required_configs = [config.TOKEN, config.ENCODING_AES_KEY, config.CORP_ID]
        default_values = ['your_token_here', 'your_encoding_aes_key_here', 'your_corp_id_here']

        for cfg, default in zip(required_configs, default_values):
            if not cfg or cfg == default:
                return False

        # 检查 EncodingAESKey 长度
        if len(config.ENCODING_AES_KEY) != 43:
            return False

        return True

    def handle_text_message(self, msg_data):
        """处理文本消息"""
        try:
            # 优先使用自定义处理器
            if self.custom_handler:
                return self.custom_handler.handle_text_message(msg_data)

            # 默认处理逻辑
            xml_tree = ET.fromstring(msg_data)
            from_user = xml_tree.find("FromUserName").text
            content = xml_tree.find("Content").text
            msg_id = xml_tree.find("MsgId").text

            logger.info(f"收到文本消息 - 用户: {from_user}, 内容: {content}, 消息ID: {msg_id}")

            # 构造回复消息
            reply_content = f"收到您的消息：{content}"
            reply_msg = self.create_text_reply(from_user, reply_content, msg_id)

            return reply_msg

        except Exception as e:
            logger.error(f"处理文本消息失败: {e}")
            return None

    def handle_event_message(self, msg_data):
        """处理事件消息"""
        try:
            # 优先使用自定义处理器
            if self.custom_handler:
                return self.custom_handler.handle_event_message(msg_data)

            # 默认处理逻辑
            xml_tree = ET.fromstring(msg_data)
            from_user = xml_tree.find("FromUserName").text
            event = xml_tree.find("Event").text

            logger.info(f"收到事件消息 - 用户: {from_user}, 事件: {event}")

            if event == "subscribe":
                reply_content = "欢迎关注！"
            elif event == "unsubscribe":
                reply_content = "感谢使用！"
            else:
                reply_content = f"收到事件：{event}"

            reply_msg = self.create_text_reply(from_user, reply_content)
            return reply_msg

        except Exception as e:
            logger.error(f"处理事件消息失败: {e}")
            return None

    def create_text_reply(self, to_user, content, msg_id=None):
        """创建文本回复消息"""
        timestamp = str(int(time.time()))

        reply_xml = f"""<xml>
<ToUserName><![CDATA[{to_user}]]></ToUserName>
<FromUserName><![CDATA[{config.CORP_ID}]]></FromUserName>
<CreateTime>{timestamp}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[{content}]]></Content>
</xml>"""

        return reply_xml

    def process_message(self, msg_data):
        """处理消息的主入口"""
        try:
            xml_tree = ET.fromstring(msg_data)
            msg_type = xml_tree.find("MsgType").text

            logger.info(f"消息类型: {msg_type}")

            if msg_type == "text":
                return self.handle_text_message(msg_data)
            elif msg_type == "event":
                return self.handle_event_message(msg_data)
            else:
                logger.info(f"暂不支持的消息类型: {msg_type}")
                return None

        except Exception as e:
            logger.error(f"处理消息失败: {e}")
            return None

message_handler = WeComMessageHandler()

@app.route('/wecom/callback', methods=['GET', 'POST'])
def wecom_callback():
    """企业微信回调接口"""
    
    if request.method == 'GET':
        # URL验证
        return handle_url_verification()
    elif request.method == 'POST':
        # 消息处理
        return handle_message()

def handle_url_verification():
    """处理URL验证"""
    try:
        msg_signature = request.args.get('msg_signature', '')
        timestamp = request.args.get('timestamp', '')
        nonce = request.args.get('nonce', '')
        echostr = request.args.get('echostr', '')

        logger.info(f"URL验证请求 - signature: {msg_signature}, timestamp: {timestamp}, nonce: {nonce}")

        # 检查配置是否有效
        if not message_handler.wxcpt:
            logger.error("企业微信配置无效，无法进行URL验证")
            return "配置无效", 403

        ret, reply_echostr = message_handler.wxcpt.VerifyURL(msg_signature, timestamp, nonce, echostr)

        if ret != 0:
            logger.error(f"URL验证失败，错误码: {ret}")
            return "验证失败", 403

        logger.info("URL验证成功")
        return reply_echostr

    except Exception as e:
        logger.error(f"URL验证异常: {e}")
        return "验证异常", 500

def handle_message():
    """处理消息"""
    try:
        msg_signature = request.args.get('msg_signature', '')
        timestamp = request.args.get('timestamp', '')
        nonce = request.args.get('nonce', '')

        # 获取POST数据
        post_data = request.get_data()

        logger.info(f"收到消息 - signature: {msg_signature}, timestamp: {timestamp}, nonce: {nonce}")

        # 检查配置是否有效
        if not message_handler.wxcpt:
            logger.error("企业微信配置无效，无法处理消息")
            return "success"  # 返回success避免重复推送

        # 解密消息
        ret, msg_data = message_handler.wxcpt.DecryptMsg(post_data, msg_signature, timestamp, nonce)

        if ret != 0:
            logger.error(f"消息解密失败，错误码: {ret}")
            return "success"  # 返回success避免重复推送

        logger.info(f"解密成功，消息内容: {msg_data}")

        # 处理消息
        reply_msg = message_handler.process_message(msg_data)

        if reply_msg:
            # 加密回复消息
            ret, encrypted_msg = message_handler.wxcpt.EncryptMsg(reply_msg, nonce, timestamp)

            if ret != 0:
                logger.error(f"消息加密失败，错误码: {ret}")
                return "success"  # 即使加密失败也返回success，避免企业微信重复推送

            logger.info("回复消息发送成功")
            response = make_response(encrypted_msg)
            response.headers['Content-Type'] = 'application/xml; charset=utf-8'
            return response

        return "success"

    except Exception as e:
        logger.error(f"处理消息异常: {e}")
        return "success"  # 返回success避免重复推送

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        "status": "ok",
        "timestamp": int(time.time()),
        "service": "wecom-callback-server"
    })

@app.route('/config', methods=['GET'])
def get_config():
    """获取配置信息（不包含敏感信息）"""
    return jsonify({
        "corp_id": config.CORP_ID,
        "token_configured": bool(config.TOKEN and config.TOKEN != 'your_token_here'),
        "aes_key_configured": bool(config.ENCODING_AES_KEY and config.ENCODING_AES_KEY != 'your_encoding_aes_key_here')
    })

if __name__ == '__main__':
    # 检查配置
    if not all([config.TOKEN, config.ENCODING_AES_KEY, config.CORP_ID]):
        logger.error("请设置环境变量: WECOM_TOKEN, WECOM_ENCODING_AES_KEY, WECOM_CORP_ID")
        sys.exit(1)
    
    # 启动服务
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('DEBUG', 'False').lower() == 'true'
    
    logger.info(f"启动企业微信回调服务，端口: {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
